// Centralized mock tools registry for Ceto Chat
// Loaded before ceto_app.js; provides window.CetoMockTools

(function () {
  if (typeof window === 'undefined') return;

  window.CetoMockTools = {
    mock_echostring: {
      description: 'Mock echo tool (fallback when MCP is unreachable)',
      input_schema: {
        type: 'object',
        properties: {
          text: { type: 'string', description: 'Text to echo' }
        },
        required: ['text']
      },
      async invoke(ctx, toolArgs = {}) {
        const fixtureUrl = '/gaia/djangaia/ceto_chat/static/mock_assets/echostring.json';
        ctx.addDebugLog('MCP_TOOL', 'SENT', `Mock call to mock_echostring using ${fixtureUrl}`);
        const mockCall = ctx.addCallDetail('GET', fixtureUrl);
        try {
          const resp = await fetch(fixtureUrl);
          const data = await resp.json();
          const textParts = (data?.result?.content || [])
            .filter(ci => ci && ci.type === 'text' && typeof ci.text === 'string')
            .map(ci => ci.text);
          const combinedText = textParts.join('');
          ctx.lastToolResponse = {
            success: true,
            text: combinedText,
            rpc: data,
            tool_name: 'mock_echostring',
            tool_args: toolArgs
          };
          ctx.updateCallDetail(mockCall, 200, data);
          ctx.addDebugLog('MCP_TOOL', 'RECEIVED', JSON.stringify(data, null, 2));
        } catch (err) {
          const errMsg = `Failed to load mock fixture: ${err.message}`;
          ctx.lastToolResponse = { success: false, error: errMsg };
          ctx.updateCallDetail(mockCall, 'ERROR', null, errMsg);
          ctx.addErrorLog(errMsg, 'callTool(mock_echostring)');
        } finally {
          ctx.mcpToolLoading = false;
        }
      }
    },

    mock_svg: {
      description: 'Mock SVG tool (returns a simple chart SVG fixture)',
      input_schema: {
        type: 'object',
        properties: {
          text: { type: 'string', description: 'Optional text (ignored by mock)' }
        }
      },
      async invoke(ctx, toolArgs = {}) {
        const fixtureUrl = '/gaia/djangaia/ceto_chat/static/mock_assets/chart3.svg';
        ctx.addDebugLog('MCP_TOOL', 'SENT', `Mock call to mock_svg using ${fixtureUrl}`);
        const mockCall = ctx.addCallDetail('GET', fixtureUrl);
        try {
          const resp = await fetch(fixtureUrl);
          const svgText = await resp.text();
          const rpc = { jsonrpc: '2.0', id: 'mock-svg-1', result: { content: [{ type: 'text', text: svgText }] } };
          ctx.lastToolResponse = {
            success: true,
            format: 'svg',
            text: svgText,
            rpc,
            tool_name: 'mock_svg',
            tool_args: toolArgs
          };
          ctx.updateCallDetail(mockCall, 200, rpc);
          ctx.addDebugLog('MCP_TOOL', 'RECEIVED', '[SVG content omitted for brevity]');
        } catch (err) {
          const errMsg = `Failed to load mock SVG fixture: ${err.message}`;
          ctx.lastToolResponse = { success: false, error: errMsg };
          ctx.updateCallDetail(mockCall, 'ERROR', null, errMsg);
          ctx.addErrorLog(errMsg, 'callTool(mock_svg)');
        } finally {
          ctx.mcpToolLoading = false;
        }
      }
    }
  };
})();

