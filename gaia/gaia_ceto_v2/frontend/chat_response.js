// Chat Response Page JavaScript
// Uses extracted utilities from ceto_app.js and mock_chat.js

window.addEventListener('DOMContentLoaded', () => {
  const { createApp } = Vue;
  const DEFAULT_MCP = 'http://127.0.0.1:9000';

  createApp({
    mixins: [window.DebugPanelMixin],
    data() {
      return {
        provider: 'mock-llm',
        MCP_API_BASE: (typeof window !== 'undefined' && window.MCP_API_BASE) ? window.MCP_API_BASE : DEFAULT_MCP,
        messages: [],
        userMessage: '',
        lastAssistantResponse: '',
        sending: false,
        mockModeActive: false,
        mcpConnected: false,
        mcpLoading: false
      };
    },
    methods: {
      // Use the generic fetchJsonOrError from ceto_app.js (via mixin or global)
      async fetchJsonOrError(url, options = {}) {
        const response = await fetch(url, options);
        const contentType = (response.headers && response.headers.get && response.headers.get('content-type')) || '';
        let data;
        if (contentType && contentType.toLowerCase().includes('application/json')) {
          try { data = await response.json(); } catch (e) { data = { success: false, error: `Invalid JSON: ${e.message}`, status: response.status }; }
        } else {
          const text = await response.text();
          data = { success: false, error: 'Non-JSON response', status: response.status, text: (text || '').slice(0, 2000) };
        }
        return { response, data };
      },

      // Check MCP server availability using generic utility
      async checkMcpServer() {
        if (window.McpServerUtils && window.McpServerUtils.checkServerAvailability) {
          await window.McpServerUtils.checkServerAvailability(this, this.MCP_API_BASE, {
            testEndpoint: '/ceto_chat/api/mcp/tools/',
            logPrefix: 'MCP_CHAT'
          });
        } else {
          // Fallback if utilities not loaded
          console.warn('McpServerUtils not available, using fallback check');
          this.mockModeActive = true;
          this.mcpConnected = false;
        }
      },

      // Send chat message using extracted utility
      async sendChat() {
        if (!this.userMessage.trim()) return;
        const messageText = this.userMessage.trim();
        this.userMessage = '';

        if (window.ChatMcpUtils && window.ChatMcpUtils.sendChatWithFallback) {
          await window.ChatMcpUtils.sendChatWithFallback(this, messageText, this.MCP_API_BASE);
        } else {
          // Fallback if utilities not loaded
          console.warn('ChatMcpUtils not available, using basic fallback');
          this.sending = true;
          
          try {
            if (typeof window.mock_chat === 'function') {
              const mockResponse = await window.mock_chat(messageText, this);
              this.mockModeActive = true;
              this.mcpConnected = false;
              
              if (mockResponse.success) {
                this.messages.push({ 
                  role: 'user', 
                  content: messageText, 
                  timestamp: new Date().toISOString() 
                });
                this.messages.push({
                  role: 'assistant',
                  content: mockResponse.message,
                  timestamp: new Date().toISOString(),
                  mock_mode: true
                });
                this.lastAssistantResponse = mockResponse.message;
              }
            } else {
              this.addErrorLog('No chat utilities available', 'sendChat');
            }
          } catch (error) {
            this.addErrorLog(`Chat error: ${error.message}`, 'sendChat');
          } finally {
            this.sending = false;
          }
        }
      }
    },
    mounted() {
      this.addDebugLog('SYSTEM', 'INFO', `Minimal MCP chat page loaded (MCP_API_BASE=${this.MCP_API_BASE})`);
      
      // Check MCP server availability on page load
      this.checkMcpServer();
    }
  }).mount('#app');
});
