// Mock Chat functionality for Ceto Chat
// Provides fallback chat responses when MCP server is unavailable

(function () {
  if (typeof window === 'undefined') return;

  // Mock chat function for fallback when MCP server is unavailable
  window.mock_chat = async function(message, ctx) {
    const mockResponses = [
      "I'm a mock LLM running in fallback mode. The MCP server on port 9000 appears to be unavailable.",
      "This is a hardcoded response from the mock chat system. Your message was: ",
      "Mock mode is active - I can't access real tools or provide dynamic responses right now.",
      "I received your message but I'm just a simple fallback. Try starting the MCP server on port 9000.",
      "Hello! I'm the emergency backup chat. Your actual assistant will be back when the MCP server is running."
    ];
    
    const randomResponse = mockResponses[Math.floor(Math.random() * mockResponses.length)];
    const reply = message.length > 50 ? 
      `${randomResponse} "${message.substring(0, 50)}..."` : 
      `${randomResponse} "${message}"`;
    
    // Log the mock chat interaction if context is provided
    if (ctx && typeof ctx.addDebugLog === 'function') {
      ctx.addDebugLog('MOCK_CHAT', 'SENT', `Mock chat called with: ${message}`);
      ctx.addDebugLog('MOCK_CHAT', 'RECEIVED', reply);
    }
    
    return {
      success: true,
      message: reply,
      mock_mode: true
    };
  };

  // Chat-specific MCP server checking functionality
  window.ChatMcpUtils = {
    // Check MCP server availability for chat functionality
    async checkMcpServer(ctx, mcpApiBase) {
      console.log('🔵 ChatMcpUtils.checkMcpServer() called');
      ctx.mcpLoading = true;
      ctx.addDebugLog('MCP', 'SENT', 'Checking MCP server availability...');

      const testUrl = `${mcpApiBase}/ceto_chat/api/mcp/tools/`;
      const callDetail = ctx.addCallDetail('GET', testUrl);

      try {
        console.log('🔵 Testing MCP server connection to:', testUrl);
        const { response, data } = await ctx.fetchJsonOrError(testUrl);
        console.log('🔵 MCP server test response status:', response.status);
        console.log('🔵 MCP server test data:', data);

        if (response.ok && data.success) {
          console.log('🟢 MCP server is available');
          ctx.mcpConnected = true;
          ctx.mockModeActive = false;
          ctx.updateCallDetail(callDetail, response.status, data);
          ctx.addDebugLog('MCP', 'RECEIVED', `MCP server available at ${mcpApiBase}`);
        } else {
          console.log('🔴 MCP server responded but not successful:', data.error);
          ctx.mcpConnected = false;
          ctx.mockModeActive = true;
          ctx.updateCallDetail(callDetail, response.status, data, data.error);
          ctx.addDebugLog('MCP', 'INFO', 'MCP server unavailable, mock mode activated');
        }
      } catch (error) {
        console.log('🟡 MCP server unavailable, activating mock mode');
        ctx.mcpConnected = false;
        ctx.mockModeActive = true;
        ctx.updateCallDetail(callDetail, 'ERROR', null, error.message);
        ctx.addDebugLog('MCP', 'INFO', `MCP server unavailable: ${error.message}. Mock mode activated.`);
      } finally {
        ctx.mcpLoading = false;
      }
    },

    // Send chat message with automatic fallback to mock chat
    async sendChatWithFallback(ctx, messageText, mcpApiBase) {
      if (!messageText.trim()) return;
      
      ctx.sending = true;
      const url = `${mcpApiBase}/chat/message`;
      const payload = { message: messageText };

      // Debug tracking
      ctx.addDebugLog('HTTP', 'SENT', payload, url);
      const call = ctx.addCallDetail('POST', url, payload);

      try {
        const { response, data } = await ctx.fetchJsonOrError(url, {
          method: 'POST', 
          headers: { 'Content-Type': 'application/json' }, 
          body: JSON.stringify(payload)
        });

        ctx.updateCallDetail(call, response.status, data);
        ctx.addDebugLog('HTTP', 'RECEIVED', data, url);

        if (response.ok && data.success) {
          ctx.mcpConnected = true; // Update connection status
          ctx.mockModeActive = false; // Reset mock mode when MCP server is working
          ctx.messages.push({ 
            role: 'user', 
            content: messageText, 
            timestamp: new Date().toISOString() 
          });
          ctx.messages.push({ 
            role: 'assistant', 
            content: data.message, 
            timestamp: new Date().toISOString() 
          });
          ctx.lastAssistantResponse = data.message;
        } else {
          throw new Error(data.error || 'Mock chat failed');
        }
      } catch (e) {
        // Fallback to mock_chat when MCP server is unavailable
        console.log('🟡 MCP server unavailable, falling back to mock_chat');
        ctx.addErrorLog(`MCP server error: ${e.message}. Using mock fallback.`, 'sendChat');

        try {
          if (typeof window.mock_chat === 'function') {
            const mockResponse = await window.mock_chat(messageText, ctx);
            ctx.updateCallDetail(call, 'MOCK', mockResponse);
            ctx.mcpConnected = false; // Update connection status
            ctx.mockModeActive = true; // Activate mock mode badge

            if (mockResponse.success) {
              ctx.messages.push({ 
                role: 'user', 
                content: messageText, 
                timestamp: new Date().toISOString() 
              });
              ctx.messages.push({
                role: 'assistant',
                content: mockResponse.message,
                timestamp: new Date().toISOString(),
                mock_mode: true
              });
              ctx.lastAssistantResponse = mockResponse.message;
            } else {
              ctx.addErrorLog('Mock chat also failed', 'sendChat');
            }
          } else {
            ctx.addErrorLog('Mock chat function not available', 'sendChat');
          }
        } catch (mockError) {
          ctx.addErrorLog(`Mock chat error: ${mockError.message}`, 'sendChat');
        }
      } finally {
        ctx.sending = false;
      }
    }
  };
})();
