<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Test</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>
<body>
<div id="app">
    <h1>{{ message }}</h1>
    <button @click="updateMessage">Click me</button>
</div>

<script>
const { createApp } = Vue;

createApp({
    data() {
        return {
            message: 'Hello Vue!'
        };
    },
    methods: {
        updateMessage() {
            this.message = 'Button clicked!';
        }
    }
}).mount('#app');
</script>
</body>
</html>
