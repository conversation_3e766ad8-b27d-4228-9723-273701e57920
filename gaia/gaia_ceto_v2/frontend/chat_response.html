<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ceto Chat Prompt – FE Level 0052</title>

    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Hardcoded asset paths (no Django template tags) -->
    <link rel="stylesheet" type="text/css" href="/gaia/djangaia/ceto_chat/static/ceto_chat/ceto_chat.css">
    <script src="/gaia/djangaia/ceto_chat/static/ceto_chat/debug.js" defer></script>
    <script src="/gaia/djangaia/ceto_chat/static/ceto_chat/mock_tools.js" defer></script>
    <script src="/gaia/djangaia/ceto_chat/static/ceto_chat/mock_chat.js" defer></script>
    <script src="/gaia/djangaia/gaia_chat/static/gaia_chat/js/chat_app.js" defer></script>

    <style>
        /* Minimal tweaks for this page */
        .response-box { background:#f5f5f5; border:1px solid #ddd; border-radius:6px; padding:12px; white-space:pre-wrap; }
        .chat-input-row { display:flex; gap:10px; align-items:center; flex-wrap:wrap; }
        .chat-input-row input[type="text"] { flex: 1 1 420px; padding:8px; border:1px solid #ccc; border-radius:4px; }
        .message-item { margin:6px 0; }
        .message-item .role { font-weight:600; margin-right:6px; color:#555; }
        .badge-id { padding: 3px 8px; font-size: 12px; line-height: 1; border-radius: 10px; background:#343a40; color:#ffc107; border:1px solid #6c757d; }
    </style>
</head>
<body>
<div id="app" class="app-container">
    <!-- Debug Panel Toggle Button -->
    <button @click="toggleDebugPanel"
            class="btn btn-sm debug-toggle"
            :class="debugPanelVisible ? 'btn-warning' : 'btn-outline-secondary'"
            style="position: fixed; top: 10px; right: 10px; z-index: 1050; background: #333; color: white; border: 1px solid #666;">
        <i class="fas fa-bug"></i> Debug
    </button>

    <div class="main-content">
        <h1 style="display:flex; align-items:center; gap:8px;">
            Ceto Chat – Minimal MCP Chat
            <span v-if="conversationId" class="badge-id">Convo: {{ conversationId.slice(0,8) }}…</span>
            <span v-if="mockModeActive"
                  style="padding: 3px 8px; font-size: 12px; line-height: 1; border-radius: 10px; background:#343a40; color:#ffc107; border:1px solid #6c757d;">
                Mock mode
            </span>
        </h1>

        <!-- Provider/Base URL controls (optional; advanced users can override via window.CHAT_API_BASE) -->
        <div style="margin: 10px 0; color:#666; font-size: 13px;">
            <span><strong>Provider:</strong> {{ provider }}</span>
            <span style="margin-left:10px;"><strong>API:</strong> {{ MCP_API_BASE || '(same origin)' }}</span>
        </div>

        <!-- MCP Connection Status -->
        <div class="mcp-status-bar" style="margin: 10px 0; padding: 8px; border-radius: 4px; font-size: 14px;"
             :style="{ backgroundColor: mcpConnected ? '#d4edda' : '#f8d7da',
                       color: mcpConnected ? '#155724' : '#721c24',
                       border: mcpConnected ? '1px solid #c3e6cb' : '1px solid #f5c6cb' }">
            <i :class="mcpConnected ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle'"></i>
            <span v-if="mcpLoading">Checking MCP server...</span>
            <span v-else-if="mcpConnected">Connected to MCP server at {{ MCP_API_BASE }}</span>
            <span v-else>MCP server unavailable - using mock chat fallback</span>
            <button @click="checkMcpServer"
                    class="btn btn-sm btn-outline-secondary"
                    style="float: right; margin-top: -2px; font-size: 12px; padding: 2px 6px;"
                    :disabled="mcpLoading">
                <i class="fas fa-sync-alt" :class="{ 'fa-spin': mcpLoading }"></i> Refresh
            </button>
        </div>

        <!-- Chat input row -->
        <div class="chat-input-row" style="margin: 12px 0;">
            <input v-model="userMessage"
                   :disabled="sending"
                   type="text"
                   placeholder="Type your prompt for the MCP LLM..." />
            <button @click="sendChat"
                    :disabled="sending || !userMessage.trim()"
                    style="padding:8px 16px; background:#007bff; color:white; border:none; border-radius:4px; cursor:pointer;"
                    :style="{ opacity: (sending || !userMessage.trim()) ? 0.6 : 1 }">
                <span v-if="sending"><i class="fas fa-spinner fa-spin"></i> Sending…</span>
                <span v-else>Send</span>
            </button>
        </div>

        <!-- Last response -->
        <div v-if="lastAssistantResponse" class="response-box" style="margin-top:12px;">
            <div style="font-weight:600; margin-bottom:6px; color:#333;">Last Response</div>
            <div v-text="lastAssistantResponse"></div>
        </div>

        <!-- Simple conversation transcript -->
        <div v-if="messages.length" style="margin-top:16px;">
            <h4 style="margin:0 0 6px 0;">Transcript</h4>
            <div v-for="(m, idx) in messages" :key="idx" class="message-item">
                <span class="role">{{ m.role }}:</span>
                <span v-text="m.content"></span>
            </div>
        </div>
    </div>

    <!-- Debug Panel (subset: Calls, API, History, Errors) -->
    <div v-if="debugPanelVisible" class="debug-panel">
        <div class="debug-header">
            <h5>Debug Console</h5>
            <div class="debug-controls">
                <button @click="resetAllState" class="btn btn-sm btn-outline-warning" title="Reset all debug state and re-initialize">
                    <i class="fas fa-redo"></i> RESET
                </button>
                <button @click="clearDebugLogs" class="btn btn-sm btn-outline-danger">Clear</button>
                <button @click="toggleDebugPanel" class="btn btn-sm btn-outline-secondary">×</button>
            </div>
        </div>

        <!-- Tabs -->
        <div class="debug-tabs">
            <button @click="setDebugPaneMode('calls')" class="debug-tab" :class="{'active': debugPaneMode === 'calls'}">Call Details</button>
            <button @click="setDebugPaneMode('api')" class="debug-tab" :class="{'active': debugPaneMode === 'api'}">API</button>
            <button @click="setDebugPaneMode('history')" class="debug-tab" :class="{'active': debugPaneMode === 'history'}">History</button>
            <button @click="setDebugPaneMode('errors')" class="debug-tab" :class="{'active': debugPaneMode === 'errors'}">Errors <span v-if="errorLog.length" class="badge badge-danger">{{ errorLog.length }}</span></button>
        </div>

        <!-- Calls Pane -->
        <div v-if="debugPaneMode === 'calls'" class="debug-content">
            <h6>HTTP Call Details</h6>
            <div v-if="callDetails.length === 0" class="debug-empty">No HTTP calls tracked yet.</div>
            <div v-else>
                <div class="call-details-table">
                    <div class="call-details-header">
                        <span>Time</span><span>Method</span><span>URL</span><span>Status</span><span>Latency</span><span>Sent</span><span>Received</span>
                    </div>
                    <div v-for="call in callDetails"
                         :key="call.id"
                         class="call-details-row"
                         :class="{'call-error': call.status >= 400 || call.status === 'ERROR', 'call-pending': !call.completed}">
                        <span class="call-time" v-text="formatTimestamp(call.timestamp)"></span>
                        <span class="call-method" v-text="call.method"></span>
                        <span class="call-url"><a href="#" @click.prevent="toggleCallExpand(call)" :title="call.url" v-text="call.url"></a></span>
                        <span class="call-status" v-text="call.status"></span>
                        <span class="call-latency" v-text="call.latency ? call.latency + ' ms' : (call.completed ? '-' : 'pending')"></span>
                        <span class="call-sent" v-text="formatBytes(call.sentBytes)"></span>
                        <span class="call-received" v-text="formatBytes(call.receivedBytes)"></span>
                        <div v-if="call.expanded" class="call-row-details">
                            <div style="margin-bottom:6px"><strong>URL:</strong> {{ call.url }}</div>
                            <div v-if="call.requestBody" class="call-detail-section">
                                <h7>Request</h7>
                                <pre class="debug-log-data" v-text="call.requestBody"></pre>
                            </div>
                            <div v-if="call.responseBody" class="call-detail-section">
                                <h7>Response</h7>
                                <pre class="debug-log-data" v-text="call.responseBody"></pre>
                            </div>
                            <div v-if="call.error" class="call-detail-section">
                                <h7>Error</h7>
                                <pre class="debug-log-data error-text" v-text="call.error"></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Pane -->
        <div v-if="debugPaneMode === 'api'" class="debug-content">
            <h6>Raw API Data</h6>
            <div v-if="debugLogs.length === 0" class="debug-empty">No debug logs yet.</div>
            <div v-for="log in debugLogs" :key="log.id" class="debug-log-entry" :class="'debug-' + log.type.toLowerCase()">
                <div class="debug-log-header">
                    <span class="debug-timestamp" v-text="formatTimestamp(log.timestamp)"></span>
                    <span class="debug-type" :class="'debug-type-' + log.type.toLowerCase()" v-text="log.type"></span>
                    <span class="debug-direction" :class="'debug-direction-' + log.direction.toLowerCase()" v-text="log.direction"></span>
                    <span v-if="log.url" class="debug-url" v-text="log.url"></span>
                </div>
                <pre class="debug-log-data" v-text="log.data"></pre>
            </div>
        </div>

        <!-- History Pane -->
        <div v-if="debugPaneMode === 'history'" class="debug-content">
            <h6>Conversation History</h6>
            <div v-if="!messages.length" class="debug-empty">No conversation history yet.</div>
            <div v-else>
                <div v-for="(m, idx) in messages" :key="idx" class="debug-log-entry">
                    <div class="debug-log-header">
                        <span class="debug-type" :class="{'debug-type-info': true}">{{ m.role }}</span>
                    </div>
                    <pre class="debug-log-data" v-text="m.content"></pre>
                </div>
            </div>
        </div>

        <!-- Errors Pane -->
        <div v-if="debugPaneMode === 'errors'" class="debug-content">
            <h6>Error Log</h6>
            <div v-if="errorLog.length === 0" class="debug-empty">No errors logged.</div>
            <div v-else>
                <div v-for="err in errorLog" :key="err.id" class="debug-log-entry debug-error">
                    <div class="debug-log-header">
                        <span class="debug-timestamp" v-text="formatTimestamp(err.timestamp)"></span>
                        <span class="debug-type debug-type-error">ERROR</span>
                        <span v-if="err.source" class="debug-direction" v-text="err.source"></span>
                    </div>
                    <pre class="debug-log-data error-text" v-text="err.message"></pre>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize the chat app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Mock Django data for standalone usage
    const djangoData = {
        hasOpenAIKey: false,
        hasAnthropicKey: false,
        chatContext: {
            title: 'Chat Response Test',
            description: 'Standalone chat interface for testing',
            data: []
        }
    };

    // Initialize the chat app
    if (typeof initializeChatApp === 'function') {
        initializeChatApp(djangoData);
    } else {
        console.error('initializeChatApp function not found');
    }
});
</script>

</body>
</html>

