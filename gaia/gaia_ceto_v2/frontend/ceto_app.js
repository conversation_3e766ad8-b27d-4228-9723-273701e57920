// Ceto Chat JavaScript v2
console.log('Ceto Chat app v2 loading...');

// Initialize Vue app with debug panel functionality
// No need for DOMContentLoaded since script is loaded with defer
const { createApp } = Vue;
// Mock tools registry is loaded from mock_tools.js before this file
if (typeof window !== 'undefined' && !window.CetoMockTools) {
    window.CetoMockTools = {};
}

// Base URL for MCP REST endpoints (can be overridden via window.MCP_API_BASE)
const MCP_API_BASE = (typeof window !== 'undefined' && window.MCP_API_BASE) ? window.MCP_API_BASE : 'http://localhost:9000';

// Generic MCP server utilities (reusable across different interfaces)
window.McpServerUtils = {
    // Generic MCP server availability check
    async checkServerAvailability(ctx, mcpApiBase, options = {}) {
        const {
            testEndpoint = '/ceto_chat/api/mcp/tools/',
            onSuccess = null,
            onFailure = null,
            logPrefix = 'MCP'
        } = options;

        console.log(`🔵 ${logPrefix} server check started`);
        if (ctx.mcpLoading !== undefined) ctx.mcpLoading = true;
        ctx.addDebugLog(logPrefix, 'SENT', 'Checking MCP server availability...');

        const testUrl = `${mcpApiBase}${testEndpoint}`;
        const callDetail = ctx.addCallDetail('GET', testUrl);

        try {
            console.log(`🔵 Testing ${logPrefix} server connection to:`, testUrl);
            const { response, data } = await ctx.fetchJsonOrError(testUrl);
            console.log(`🔵 ${logPrefix} server test response status:`, response.status);
            console.log(`🔵 ${logPrefix} server test data:`, data);

            if (response.ok && data.success) {
                console.log(`🟢 ${logPrefix} server is available`);
                if (ctx.mcpConnected !== undefined) ctx.mcpConnected = true;
                if (ctx.mockModeActive !== undefined) ctx.mockModeActive = false;
                ctx.updateCallDetail(callDetail, response.status, data);
                ctx.addDebugLog(logPrefix, 'RECEIVED', `${logPrefix} server available at ${mcpApiBase}`);

                if (onSuccess) onSuccess(ctx, data);
            } else {
                console.log(`🔴 ${logPrefix} server responded but not successful:`, data.error);
                if (ctx.mcpConnected !== undefined) ctx.mcpConnected = false;
                if (ctx.mockModeActive !== undefined) ctx.mockModeActive = true;
                ctx.updateCallDetail(callDetail, response.status, data, data.error);
                ctx.addDebugLog(logPrefix, 'INFO', `${logPrefix} server unavailable, mock mode activated`);

                if (onFailure) onFailure(ctx, data.error);
            }
        } catch (error) {
            console.log(`🟡 ${logPrefix} server unavailable, activating mock mode`);
            if (ctx.mcpConnected !== undefined) ctx.mcpConnected = false;
            if (ctx.mockModeActive !== undefined) ctx.mockModeActive = true;
            ctx.updateCallDetail(callDetail, 'ERROR', null, error.message);
            ctx.addDebugLog(logPrefix, 'INFO', `${logPrefix} server unavailable: ${error.message}. Mock mode activated.`);

            if (onFailure) onFailure(ctx, error.message);
        } finally {
            if (ctx.mcpLoading !== undefined) ctx.mcpLoading = false;
        }
    }
};


const app = createApp({
    // Mix in debug panel functionality
    mixins: [window.DebugPanelMixin],

    data() {
        return {
            appVersion: '2.0.0',
            appName: 'Ceto Chat',
            // MCP-related data
            mcpTools: [],
            mcpServerUrl: '',
            mcpConnected: false,
            mcpLoading: false,
            selectedToolName: '',
            // Level 0032: Tool testing
            testMessage: 'Hello from frontend!',
            mcpToolLoading: false,
            lastToolResponse: null,
            // UI preference: toggle visibility of full JSON-RPC payloads
            showFullJsonRpc: false,
            // Mock mode indicator (true when using local mock tools)
            mockModeActive: false
        };
    },

    methods: {
            // Safe JSON fetch helper: returns { response, data } where data is JSON or a structured error object
            async fetchJsonOrError(url, options = {}) {
                const response = await fetch(url, options);
                const contentType = (response.headers && response.headers.get && response.headers.get('content-type')) || '';
                let data;
                if (contentType && contentType.toLowerCase().includes('application/json')) {
                    try {
                        data = await response.json();
                    } catch (e) {
                        data = { success: false, error: `Invalid JSON from server: ${e.message}`, status: response.status };
                    }
                } else {
                    const text = await response.text();
                    data = {
                        success: false,
                        error: 'Non-JSON response from server',
                        status: response.status,
                        content_type: contentType || 'unknown',
                        text: (text || '').slice(0, 2000)
                    };
                }
                return { response, data };
            },
        // Load MCP tools from server
        async loadMcpTools() {
            console.log('🔵 loadMcpTools() called');
            this.mcpLoading = true;
            this.addDebugLog('MCP', 'SENT', 'Loading MCP tools...');

            const callDetail = this.addCallDetail('GET', `${MCP_API_BASE}/ceto_chat/api/mcp/tools/`);

            try {
                console.log('🔵 Fetching MCP tools from /ceto_chat/api/mcp/tools/');
                const { response, data } = await this.fetchJsonOrError(`${MCP_API_BASE}/ceto_chat/api/mcp/tools/`);
                console.log('🔵 MCP tools response status:', response.status);
                console.log('🔵 MCP tools data:', data);

                if (response.ok && data.success) {
                    console.log('🟢 MCP tools loaded successfully');
                    this.mcpTools = data.tools || [];
                    this.mcpServerUrl = data.server_url || '';
                    this.mcpConnected = true;
                    this.mockModeActive = false;

                    this.updateCallDetail(callDetail, response.status, data);
                    this.addDebugLog('MCP', 'RECEIVED', `Loaded ${this.mcpTools.length} tools from ${this.mcpServerUrl}`);

                    console.log('MCP tools loaded:', this.mcpTools);
                    console.log('MCP server URL:', this.mcpServerUrl);
                    console.log('MCP connected:', this.mcpConnected);
                } else {
                    console.log('🔴 Failed to load MCP tools:', data.error);
                    this.mcpConnected = false;
                    this.updateCallDetail(callDetail, response.status, data, data.error);
                    this.addErrorLog(data.error || 'Failed to load MCP tools', 'loadMcpTools');

                    // Fallback to mock tools when response is not OK or success is false
                    console.log('🟡 Fallback to mock tools');
                    this.activateMockTools();
                }
            } catch (error) {
                this.mcpConnected = false;
                this.updateCallDetail(callDetail, 'ERROR', null, error.message);
                this.addErrorLog(`Network error loading MCP tools: ${error.message}`, 'loadMcpTools');
                console.error('Error loading MCP tools:', error);

                // Fallback to mock tools on network error
                this.activateMockTools();
            } finally {
                this.mcpLoading = false;
            }
        },

            // Fallback: activate mock tools when MCP tools endpoint is unreachable
            activateMockTools() {
                console.log('🟡 Activating mock tools fallback');
                this.mcpTools = Object.entries(window.CetoMockTools).map(([name, def]) => ({
                    name,
                    description: def.description,
                    input_schema: def.input_schema || {}
                }));
                this.mcpServerUrl = 'mock://local-fixture';
                this.mcpConnected = false;
                this.mockModeActive = true;
                this.addDebugLog('MCP', 'INFO', 'Using mock tools fallback');
            },

        // Generic MCP Tool Caller
        async callTool(toolName, toolArgs = {}) {
                console.log('BUTTON WAS PRESSED!');
                console.log(`🔵 callTool() called with tool: ${toolName}`);
                console.log('🔵 toolArgs:', toolArgs);

                console.log('🔵 Setting loading state and clearing previous response');
                this.mcpToolLoading = true;
                this.lastToolResponse = null;

                // Mock fallback: if this is a registered mock tool, delegate to registry
                if (window.CetoMockTools && window.CetoMockTools[toolName]) {
                    await window.CetoMockTools[toolName].invoke(this, toolArgs);
                    return;
                }

                const requestData = {
                    tool_name: toolName,
                    tool_args: toolArgs
                };

                console.log('🔵 Request data:', requestData);
                this.addDebugLog('MCP_TOOL', 'SENT', `Calling ${toolName} with args: ${JSON.stringify(toolArgs)}`);
                const callDetail = this.addCallDetail('POST', `${MCP_API_BASE}/ceto_chat/api/mcp/call/`, requestData);

                try {
                    console.log('🔵 Making fetch request to /ceto_chat/api/mcp/call/');
                    const response = await fetch(`${MCP_API_BASE}/ceto_chat/api/mcp/call/`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(requestData)
                    });

                    console.log('🔵 Response status:', response.status);
                    const data = await response.json();
                    console.log('🔵 Response data:', data);

                    // Handle JSON-RPC 2.0 response format
                    if (response.ok && data && data.jsonrpc === '2.0') {
                        if (data.result && Array.isArray(data.result.content)) {
                            const textParts = data.result.content
                                .filter(ci => ci && ci.type === 'text' && typeof ci.text === 'string')
                                .map(ci => ci.text);
                            const combinedText = textParts.join('');

                            this.lastToolResponse = {
                                success: true,
                                text: combinedText,
                                rpc: data,
                                tool_name: data.tool_name || toolName,
                                tool_args: data.tool_args || toolArgs
                            };

                            this.updateCallDetail(callDetail, response.status, data);
                            this.addDebugLog('MCP_TOOL', 'RECEIVED', JSON.stringify(data, null, 2));
                            console.log(`${toolName} tool response (JSON-RPC):`, data);
                        } else if (data.error) {
                            const errMsg = (data.error && (data.error.message || data.error)) || `Failed to call ${toolName} tool`;
                            this.lastToolResponse = {
                                success: false,
                                error: errMsg,
                                rpc: data,
                                tool_name: data.tool_name || toolName,
                                tool_args: data.tool_args || toolArgs
                            };

                            this.updateCallDetail(callDetail, response.status, data, errMsg);
                            this.addErrorLog(errMsg, 'callTool');
                        } else {
                            const errMsg = 'Unexpected JSON-RPC response shape';
                            this.lastToolResponse = { success: false, error: errMsg, rpc: data };
                            this.updateCallDetail(callDetail, response.status, data, errMsg);
                            this.addErrorLog(errMsg, 'callTool');
                        }
                    } else {
                        const errMsg = (data && (data.error || data.message)) || `Failed to call ${toolName} tool`;
                        this.lastToolResponse = {
                            success: false,
                            error: errMsg,
                            raw: data
                        };

                        this.updateCallDetail(callDetail, response.status, data, errMsg);
                        this.addErrorLog(errMsg, 'callTool');
                    }
                } catch (error) {
                    console.log('🔴 Network error:', error);
                    this.lastToolResponse = {
                        success: false,
                        error: `Network error: ${error.message}`
                    };

                    this.updateCallDetail(callDetail, 'ERROR', null, error.message);
                    this.addErrorLog(`Network error calling ${toolName} tool: ${error.message}`, 'callTool');
                    console.error(`Error calling ${toolName} tool:`, error);
                } finally {
                    console.log('🔵 Setting mcpToolLoading = false');
                    this.mcpToolLoading = false;
                }
            },



    },

    mounted() {
        console.log(`${this.appName} v${this.appVersion} initialized`);

        // Add initial debug log
        this.addDebugLog('SYSTEM', 'INFO', `${this.appName} v${this.appVersion} started`);

        // Load MCP tools on startup
        this.loadMcpTools();


    }
    });

// Mount the app
app.mount('#app');

console.log('Ceto Chat app v2 mounted successfully');
