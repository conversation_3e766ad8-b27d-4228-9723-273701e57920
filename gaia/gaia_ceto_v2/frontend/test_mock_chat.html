<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mock Chat Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .mock-badge { padding: 3px 8px; font-size: 12px; border-radius: 10px; background:#343a40; color:#ffc107; border:1px solid #6c757d; }
        .result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        button { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        input[type="text"] { padding: 8px; margin: 5px; border: 1px solid #ccc; border-radius: 4px; width: 300px; }
    </style>
</head>
<body>
    <h1>Mock Chat Test <span class="mock-badge">Mock mode</span></h1>

    <div class="test-section">
        <h3>Page Load Detection Test</h3>
        <p>Open the <a href="chat_response.html" target="_blank">chat_response.html</a> page to see mock mode detection on page load.</p>
        <p><strong>Expected behavior:</strong></p>
        <ul>
            <li>If MCP server is running on port 9000: Normal mode, no mock badge</li>
            <li>If MCP server is NOT running: Mock mode badge appears immediately on page load</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h3>Test Mock Chat Function</h3>
        <p>This tests the window.mock_chat function directly.</p>
        
        <input type="text" id="testMessage" placeholder="Enter test message..." value="Hello, this is a test!">
        <button onclick="testMockChat()">Test Mock Chat</button>
        
        <div id="mockResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h3>Test MCP Server Fallback</h3>
        <p>This tests the actual fallback behavior when MCP server is unavailable.</p>
        
        <input type="text" id="mcpMessage" placeholder="Enter message for MCP test..." value="Test MCP fallback">
        <button onclick="testMcpFallback()">Test MCP with Fallback</button>
        
        <div id="mcpResult" class="result" style="display: none;"></div>
    </div>

    <!-- Load mock tools script -->
    <script src="/gaia/djangaia/ceto_chat/static/ceto_chat/mock_tools.js"></script>
    
    <script>
        // Simple mock context for testing
        const mockContext = {
            addDebugLog: function(type, level, message) {
                console.log(`[${type}] ${level}: ${message}`);
            }
        };

        async function testMockChat() {
            const message = document.getElementById('testMessage').value;
            const resultDiv = document.getElementById('mockResult');
            
            try {
                if (typeof window.mock_chat === 'function') {
                    const response = await window.mock_chat(message, mockContext);
                    
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>Mock Chat Response:</strong><br>
                        Success: ${response.success}<br>
                        Message: ${response.message}<br>
                        Mock Mode: ${response.mock_mode}
                    `;
                } else {
                    throw new Error('window.mock_chat function not found');
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
            }
            
            resultDiv.style.display = 'block';
        }

        async function testMcpFallback() {
            const message = document.getElementById('mcpMessage').value;
            const resultDiv = document.getElementById('mcpResult');
            
            // Simulate the MCP server call with intentional failure
            const badUrl = 'http://localhost:9999/chat/message'; // Wrong port to force failure
            const payload = { message: message };
            
            try {
                // This should fail and trigger fallback
                const response = await fetch(badUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `<strong>MCP Server Response:</strong> ${data.message}`;
                
            } catch (error) {
                // Fallback to mock_chat
                console.log('MCP server failed, using fallback...');
                
                try {
                    if (typeof window.mock_chat === 'function') {
                        const mockResponse = await window.mock_chat(message, mockContext);
                        
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `
                            <strong>Fallback Used!</strong><br>
                            MCP Error: ${error.message}<br>
                            Mock Response: ${mockResponse.message}<br>
                            <em>This demonstrates the fallback working correctly.</em>
                        `;
                    } else {
                        throw new Error('Mock fallback also unavailable');
                    }
                } catch (mockError) {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>Both MCP and Mock failed:</strong> ${mockError.message}`;
                }
            }
            
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
