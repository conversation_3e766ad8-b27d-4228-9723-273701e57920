# Document
- LLMs do NOT write to this file, ONLY HUMANS
- this file is HIGH LEVEL enough to use most of it for other projects

# RULES
- decoupled modules where sensible
- easy-to-test is a top priority
- easy-to-debug is a top priority
- concise, short, relatively simple code is top priority

## Testing
- test resulst must be clearly logged to tests_log/ folder
- tests logs must contain enough information to show what the module in question is doing, IE. pass/fail is NOT enough!
- test_log_inspector.py (calls LLM) is used to inspect a test log file and rate responses, and catch more failure modes and bad tests
- browser testing: 
   pyenv activate agbase311 && export $(cat ./environment_sam_local | xargs) && python manage.py runserver 127.0.0.1:8000

## Levelization
- each layer adds single concern, can be tested without layers above (and sometimes, on its own)
- ultimately whole system can be tested at multiple levels and E2E


## HOW TO RUN

#### Django dev server
cd ~/django-projects/agbase_admin/gaia/djangaia && pyenv activate agbase311 && export PYTHONPATH=~/django-projects/agbase_admin:$PYTHONPATH && export DJANGO_SETTINGS_MODULE=djangaia.settings && python manage.py runserver 127.0.0.1:8000


## LEVELS

### level 0001: standalone HTML-only tool-result renderer
- No Django, no frameworks, no build tooling
- A single static HTML page that:
  - Loads a tiny JS module to fetch a local JSON fixture (standard MCP JSON-RPC format)
  - Pretty-prints and renders it inside a gray monospace "code box"
  - Shows a compact error banner on failures
- Directory layout:
  - gaia/gaia_ceto_v2/fe_levels/000_standalone_html/
    - index.html
    - styles.css
    - app.js
    - fixtures/echostring.json (valid sample MCP JSON-RPC result)
- Controls:
  - "Load sample tool result" button triggers fetch of fixtures/echostring.json
  - Success: pretty-printed JSON inside gray box using more horizontal space
  - Error: compact red error banner with message and exception
- No state management, no debug panel at this level (pure renderer validation)

#### validate
- Serve the folder statically (no Django):
  - cd gaia/gaia_ceto_v2/fe_levels/000_standalone_html
  - python3 -m http.server 8080
  - Visit http://127.0.0.1:8080/
- Click "Load sample tool result":
  - JSON displays pretty-printed in a gray monospace code box
  - No console or network errors
- Negative test (optional): temporarily rename fixtures/echostring.json to confirm the error banner appears

### level 0002: standalone SVG renderer
- No Django, no frameworks, no build tooling
- A single static HTML page that:
  - Loads and displays an SVG file from the fixtures directory
  - Shows the SVG correctly rendered on the page
  - Handles loading errors gracefully with error banner
- Directory layout:
  - gaia/gaia_ceto_v2/fe_levels/002_standalone_svg/
    - index.html
    - styles.css
    - app.js
    - fixtures/sample.svg (valid SVG file)
- Controls:
  - "Load SVG" button triggers fetch of fixtures/sample.svg
  - Success: SVG displays inline on the page
  - Error: compact red error banner with message and exception
- Reuses styling patterns from level 0001 for consistency

#### validate
- Serve the folder statically (no Django):
  - cd gaia/gaia_ceto_v2/fe_levels/002_standalone_svg
  - python3 -m http.server 8081
  - Visit http://127.0.0.1:8081/
- Click "Load SVG":
  - SVG displays correctly rendered on the page
  - No console or network errors
- Negative test (optional): temporarily rename fixtures/sample.svg to confirm the error banner appears

### level 0003: standalone Vue + debug panel
- No Django, no build tooling
- A static page that:
  - Loads Vue.js via CDN (Vue 3 global build)
  - Includes a reusable debug panel (debug.js) with toggle button and log area
  - Fetches fixtures/echostring.json and renders pretty JSON in a gray code box
  - Mirrors the rendering from level 0001 but using Vue state/methods
- Directory layout:
  - gaia/gaia_ceto_v2/fe_levels/003_standalone_vue_debug/
    - index.html
    - styles.css
    - app.js
    - debug.js
    - fixtures/echostring.json
- Controls:
  - "Load sample tool result" button triggers fetch of fixtures/echostring.json
  - Debug toggle shows/hides panel; panel logs request/response and errors
- Reuses gray code box styling and compact spacing from previous levels

#### validate
- Serve the folder statically (no Django):
  - cd gaia/gaia_ceto_v2/fe_levels/003_standalone_vue_debug
  - python3 -m http.server 8082
  - Visit http://127.0.0.1:8082/
- Click "Load sample tool result":
  - Result displays as pretty-printed JSON; debug panel shows a request and response entry
  - No console or network errors

### level 0004: standalone chat + debug panel
- No Django, no MCP, no auth, no templating
- A static page that:
  - Loads Vue.js via CDN (Vue 3 global build)
  - Includes the reusable debug panel (debug.js) with toggle button and log area
  - Accepts user text input and, on send, fetches fixtures/echostring.json
  - Renders JSON responses as pretty-printed code blocks in a gray box
- Directory layout:
  - gaia/gaia_ceto_v2/fe_levels/004_standalone_chat/
    - chat.html
    - chat.js
    - styles.css
    - debug.js
    - fixtures/echostring.json
- Validate
  - cd gaia/gaia_ceto_v2/fe_levels/004_standalone_chat
  - python3 -m http.server 8083
  - Visit http://127.0.0.1:8083/gaia/gaia_ceto_v2/fe_levels/004_standalone_chat/chat.html
  - Type and send a message; expect user bubble + assistant JSON code block and debug logs

### level 0005: structure
- a new django app named ceto_chat in ./gaia/djangaia
- single views.py file which renders an html template called ceto_chat_base.html
- ceto_chat_base.html loads the Vue.js library
- ceto_chat_base.html loads a javascript file name ceto_app.js and a file named ceto_chat.css in the head section.
- ceto_chat_base.html renders the html markup <h1>Ceto Chat</h1>

#### validate
- app and html page load in the browser at http://127.0.0.1:8000/ceto_chat/ and renders the html template without errors


### level 0006: debugability
- ceto_chat_base.html loads a file named debug.js which contains the logic for the in-page debug panel created in v1
- at this level the debug panel is not connected to any functionality, it is just a static panel that can be toggled on and off


#### validate
- app and html page load in the browser at http://127.0.0.1:8000/ceto_chat/ and renders the html template without errors 
- debug panel is visible and functional


### level 0063: connect to mcp server
- connect django app to the running mcp server
- access MCP from django project: gaia/djangaia django app: gaia/djangaia/gaiachat_v2 
- look at gaia/djangaia/gaiachat/ for vague inspiration (dont copy)
- use standard MCP protocol format for django MCP responses
- avoid seperate django and MCP processes

#### validate
- tool list should be visible in debug panel

### level 0065: seprate MCP API concerns
- abstract MCP API loging in djangaia/cet_chat to api_mcp.py
- this should be as simple as possible
- assume only a single MCP server for now and MCP server details should be placed in djangaia/settings
- views.py should import from api_mcp.py 

#### validate
- tool list should be visible in debug panel


### level 0066: logging from fontend to backend
- add a button to the html template which calls the test MCP tool echostring (temprorary - will be reomved in next higher levels)
- write response to an html div on the page (temprorary - will be reomved in next higher levels)
- request and response should be clearly visible in the debug panel

#### validate
- request and response should be clearly visible on the page and in the debug panel

### level 0067: tool result format
- ensure echsotring tool result is in the standard MCP format (see example below)
- ensure tool result is visible in debug panel
- ensure tool result is visible on the page

#### validate
- validate tool result is valid JSON and in the standard MCP format (see example below)

#### example tool result format
```json
{
  "jsonrpc": "2.0",
  "id": "42",
  "result": {
    "content": [
      {
        "type": "text",
        "text": "Hello from MCP!"
      }
    ]
  }
}
```

### level 0068: frontend API request logs
- for tool calls, if the JSON-RPC response has an id, that id becomes the key, otherwise a generated id is used
- logged payloads include method, path, query/body, full response, duration_ms

#### validate
- tail -f /tmp/gaia_logs/ceto/cetoapi*.log


### level 0069: move tool testing to it's own url
- tool testind dropddown should be served from /ceto_chat/test
- root url should be /ceto_chat/
- both pages should inherit from ceto_chat_base.html and include the debug panel
  - test page only should include the tool dropdown
  - root page should show only a page title for now
