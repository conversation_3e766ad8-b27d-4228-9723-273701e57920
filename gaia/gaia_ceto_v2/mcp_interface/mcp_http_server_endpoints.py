#!/usr/bin/env python3
"""
Gaia CETO v2 MCP HTTP Server (with REST endpoints for Ceto Chat)

Adds REST endpoints compatible with the current frontend (ceto_app.js):
- GET  /ceto_chat/api/mcp/tools/
- POST /ceto_chat/api/mcp/call/

Also mounts the FastMCP HTTP app at /mcp as before.

This keeps the original mcp_http_server functionality and extends it with
browser-friendly endpoints so the Vue app can talk directly to port 9000.
"""

import argparse
import logging
import uuid
import inspect
from pathlib import Path
from typing import Any, Dict, List, Optional

from fastmcp import FastMCP
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Import core and tools using absolute imports
from gaia_ceto_v2.core.mcp_wrapper import log_mcp_tool_call
from gaia_ceto_v2.tools.tools import echostring as core_echostring
from gaia_ceto_v2.mcp_interface.mcp_server import MCPServer
from gaia_ceto_v2.core.tool_response import ToolResponse

logger = logging.getLogger(__name__)

# Single FastMCP instance
mcp = FastMCP("gaia_ceto_v2_http_server")


@mcp.tool()
def echostring(text: str) -> str:
    """Echoes the given text."""
    # Level 0007: Log MCP tool call
    with log_mcp_tool_call("echostring", {"text": text}, server_name="gaia_ceto_v2") as call:
        result = core_echostring(text)
        call.log_success(result)
        return result


# ---------- Helpers to introspect tools and invoke them locally ----------

def _get_tool_registry(mcp_instance) -> Optional[Any]:
    tm = getattr(mcp_instance, "_tool_manager", None)
    if tm is None:
        return None
    registry = (
        getattr(tm, "tools", None)
        or getattr(tm, "_tools_by_name", None)
        or getattr(tm, "_tools", None)
    )
    return registry


def _list_registered_tools(mcp_instance) -> List[Dict[str, Any]]:
    """Return tool list as [{name, description, input_schema}] best-effort."""
    tools: List[Dict[str, Any]] = []
    registry = _get_tool_registry(mcp_instance)

    try:
        if isinstance(registry, dict):
            for name, obj in registry.items():
                desc = getattr(obj, "description", None) or getattr(obj, "__doc__", None) or "No description"
                tools.append({
                    "name": name,
                    "description": desc.strip() if isinstance(desc, str) else "No description",
                    "input_schema": {}
                })
        elif isinstance(registry, list):
            # Some FastMCP versions use a list of tool wrappers
            seen = set()
            for obj in registry:
                name = getattr(obj, "name", None) or getattr(obj, "__name__", None)
                if not name or name in seen:
                    continue
                seen.add(name)
                desc = getattr(obj, "description", None) or getattr(obj, "__doc__", None) or "No description"
                tools.append({
                    "name": name,
                    "description": desc.strip() if isinstance(desc, str) else "No description",
                    "input_schema": {}
                })
        else:
            # Fallback: we at least know "echostring"
            tools.append({
                "name": "echostring",
                "description": "Echoes the given text.",
                "input_schema": {}
            })
    except Exception as e:
        logger.warning(f"Could not fully introspect tools: {e}")
        # Provide minimal fallback
        if not tools:
            tools.append({"name": "echostring", "description": "Echoes the given text.", "input_schema": {}})

    return tools


def _find_tool_callable(mcp_instance, tool_name: str):
    registry = _get_tool_registry(mcp_instance)
    if isinstance(registry, dict):
        obj = registry.get(tool_name)
        if obj is not None:
            # Common attributes where the original function may be stored
            for attr in ("fn", "func", "callable", "handler"):
                fn = getattr(obj, attr, None)
                if callable(fn):
                    return fn
            # If the wrapper itself is callable
            if callable(obj):
                return obj
    elif isinstance(registry, list):
        for obj in registry:
            name = getattr(obj, "name", None) or getattr(obj, "__name__", None)
            if name == tool_name:
                for attr in ("fn", "func", "callable", "handler"):
                    fn = getattr(obj, attr, None)
                    if callable(fn):
                        return fn
                if callable(obj):
                    return obj
    return None


# ---------- FastAPI app with REST endpoints and mounted MCP app ----------

class ToolCallRequest(BaseModel):
    tool_name: str
    tool_args: Dict[str, Any] = {}

class ChatMessageRequest(BaseModel):
    message: str


def create_app(host: str, port: int) -> FastAPI:
    app = FastAPI(title="Gaia CETO v2 MCP HTTP Server", version="2.0.0")

    # CORS: allow browser clients (tighten as needed)
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"]
        ,
        allow_headers=["*"]
    )

    # Compute server_url for responses
    base_server_url = f"http://{host}:{port}/mcp"

    # Add REST endpoints expected by ceto_app.js
    @app.get("/ceto_chat/api/mcp/tools/")
    async def list_tools_endpoint():
        tools = _list_registered_tools(mcp)
        return {
            "success": True,
            "tools": tools,
            "server_url": base_server_url,
            "tool_count": len(tools)
        }

    @app.post("/ceto_chat/api/mcp/call/")
    async def call_tool_endpoint(payload: ToolCallRequest):
        tool_name = payload.tool_name
        tool_args = payload.tool_args or {}
        rpc_id = str(uuid.uuid4())

        try:
            fn = _find_tool_callable(mcp, tool_name)
            if fn is None:
                return {
                    "jsonrpc": "2.0",
                    "id": rpc_id,
                    "error": {"code": -32000, "message": f"Unknown tool: {tool_name}"},
                    "tool_name": tool_name,
                    "tool_args": tool_args
                }

            # Execute tool (supports sync or async)
            if inspect.iscoroutinefunction(fn):
                result = await fn(**tool_args)
            else:
                result = fn(**tool_args)

            # Normalize to text for frontend display
            if isinstance(result, dict) and "content" in result and isinstance(result["content"], list):
                # Already MCP-like; pass through as text concatenation
                text_parts = []
                for ci in result["content"]:
                    if isinstance(ci, dict) and ci.get("type") == "text" and isinstance(ci.get("text"), str):
                        text_parts.append(ci["text"])
                combined_text = "".join(text_parts) if text_parts else str(result)
            else:
                combined_text = str(result)

            # Emit ToolResponse flags (if available) for frontend visibility
            if isinstance(result, ToolResponse):
                flags = {
                    "needs_synthesis": bool(getattr(result, "needs_synthesis", False)),
                    "show_to_user": bool(getattr(result, "show_to_user", True)),
                    "store_as_document": bool(getattr(result, "store_as_document", False)),
                }
            else:
                # Defaults when tool does not return ToolResponse
                flags = {
                    "needs_synthesis": 'defualt',
                    "show_to_user": True,
                    "store_as_document": False,
                }

            return {
                "jsonrpc": "2.0",
                "id": rpc_id,
                "result": {
                    "content": [{"type": "text", "text": combined_text}],
                    "flags": flags,
                },
                "tool_name": tool_name,
                "tool_args": tool_args
            }
        except Exception as e:
            logger.exception(f"Error calling tool '{tool_name}': {e}")
            return {
                "jsonrpc": "2.0",
                "id": rpc_id,
                "error": {"code": -32000, "message": str(e)},
                "tool_name": tool_name,
                "tool_args": tool_args
            }

    @app.post("/chat/message")
    async def chat_message(payload: ChatMessageRequest):
        """Return a hardcoded mock LLM response for a given message.
        Minimal endpoint for frontend testing without Django or tools.
        """
        import random

        mock_responses = [
            "I'm a mock LLM running on the MCP server (port 9000). I can help you test the chat interface!",
            "Hello! I'm the MCP server's built-in mock chat. Your message was: ",
            "This is a test response from the MCP HTTP server. I received: ",
            "Mock LLM here! I'm running on port 9000 and ready to chat. You said: ",
            "I'm the fallback chat assistant built into the MCP server. Your input: "
        ]

        base_response = random.choice(mock_responses)
        message_text = payload.message

        # Truncate long messages for cleaner responses
        if len(message_text) > 100:
            reply = f"{base_response} \"{message_text[:100]}...\""
        else:
            reply = f"{base_response} \"{message_text}\""

        return {"success": True, "message": reply}


    # Mount FastMCP HTTP app under /mcp for full MCP protocol support
    app.mount("/mcp", mcp.http_app())

    return app


def main():
    """Main entry point for HTTP MCP server with REST endpoints."""
    parser = argparse.ArgumentParser(description="Gaia CETO v2 MCP HTTP Server (with REST endpoints)")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=9000, help="Port to bind to")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    args = parser.parse_args()

    # Configure logging
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(level=log_level, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    logger.info("=" * 60)
    logger.info("🚀 GAIA CETO v2 MCP HTTP SERVER - With REST Endpoints")
    logger.info("=" * 60)
    logger.info(f"🌐 MCP Protocol URL: http://{args.host}:{args.port}/mcp")
    logger.info("📋 Purpose: Provide REST endpoints for Django/Vue integration + MCP protocol")
    logger.info("=" * 60)

    # Load MCP server configuration and start external servers (same as original)
    config_path = Path(__file__).parent / "server_config.json"
    server_manager = MCPServer(config_path)
    server_manager.start_all_external_servers()

    try:
        import uvicorn
        app = create_app(args.host, args.port)
        uvicorn.run(app, host=args.host, port=args.port, log_level=("debug" if args.debug else "info"))
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        raise
    finally:
        # Ensure external servers are stopped on shutdown
        try:
            server_manager.stop_all_external_servers()
        except Exception as stop_err:
            logger.warning(f"Error while stopping external MCP servers: {stop_err}")


if __name__ == "__main__":
    main()

